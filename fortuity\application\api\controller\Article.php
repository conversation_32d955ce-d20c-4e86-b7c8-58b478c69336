<?php

namespace app\api\controller;


use app\api\service\Alternative;
use app\api\service\TmplService;
use app\api\service\Util;
use think\Cache;
use think\Db;


class Article extends Base
{
    /**
     * 使用贝壳发帖
     */
    public function add_circle_new()
    {
        $data = input('param.');
        //过滤html
        $data['content']=strip_tags($data['content']);
        $data['title']=strip_tags($data['title']);
        /**
         * 第一个版本低于第二个版本的时候 return -1
         * 第一个版本等于第二个版本的时候 return 0
         * 第一个版本高于第二个版本的时候 return 1
         */
        //获取是否打开强制手机号
        $authority = Db::name('authority')->where('much_id', $data['much_id'])->find();
        $util = new Util();
        $plug = new Plugunit();
        if ($this->version == 0) {
            if ($authority['force_phone_arbor'] == 1 && empty($this->user_info['user_phone'])) {
                return $this->json_rewrite(['status' => 'error', 'id' => $this->version, 'msg' => '请绑定手机号！']);
            }
        }
        if ($this->user_info['tourist'] == 1) {
            return $this->json_rewrite(['status' => 'error', 'id' => 0, 'msg' => '请登陆后发帖！']);
        }

        //查询是否有权限
        //查询当前用户是否是会员
        $this_user_vip = $util->get_user_vip($this->user_info['id']);
        if ($this_user_vip == 0) {
            //语音
            if ($authority['voice_member'] == 1 && $data['type'] == 1) {
                return $this->json_rewrite(['status' => 'error', 'id' => 0, 'msg' => '系统开启了会员发布，您暂不是会员！']);
            }
            //视频
            if ($authority['video_member'] == 1 && $data['type'] == 2) {
                return $this->json_rewrite(['status' => 'error', 'id' => 0, 'msg' => '系统开启了会员发布，您暂不是会员！']);
            }
        }
        if ($authority['title_arbor'] == 1) {
            //标题为空
            if (empty(preg_replace('# #', '', $data['title']))) {
                $rs = ['status' => 'error', 'msg' => '标题不能为空'];
                return $this->json_rewrite($rs);
            }
        }

        $cg = Db::name('user_maker')->where('much_id', $data['much_id'])->where('user_open_id', $data['openid'])->where('status', '1')->count();

        $da_or_xiao = $util->check_qq($data['openid'], $data['fa_class']);

        if ($cg == 0 && $da_or_xiao == 'no') {
            //发帖等级检测
            $ttr = Db::name('territory')->where('much_id', $data['much_id'])->where('id', $data['fa_class'])->find();
            if ($this->user_info['level'] < $ttr['release_level']) {
                $rs = ['status' => 'error', 'msg' => 'Lv.' . $ttr['release_level'] . '等级才能发帖'];
                return $this->json_rewrite($rs);
            }
            //圈子发帖限制
            if ($ttr['release_count'] != 0) {
                //今日发帖数量
                $check_today_terr = Db::name('paper')->where('tory_id', $ttr['id'])->where('much_id', $data['much_id'])->where('user_id', $this->user_info['id'])->whereTime('adapter_time', 'today')->count();
                if ($check_today_terr >= $ttr['release_count']) {
                    return $this->json_rewrite(['status' => 'error', 'id' => 0, 'msg' => '该' . $this->design['landgrave'] . '发帖已达上限！']);
                }
            }
        }


        //检测字符
        //是否开启网络验证
        if (!empty($data['content'])) {
            $check_content = $util->get_check_msg($data['content'], $data['much_id'], $data['openid']);
            if ($check_content['status'] == 'error') {
                return $this->json_rewrite($check_content);
            }

        }
        if (!empty($data['title'])) {
            $check_title = $util->get_check_msg($data['title'], $data['much_id'], $data['openid']);
            if ($check_title['status'] == 'error') {
                return $this->json_rewrite($check_title);
            }

        }
        $option = [];
        if ($data['type'] == 4 || $data['type'] == 5) {
            $option = json_decode($data['option'], true);
            foreach ($option as $k => $v) {
                $check_option = $util->get_check_msg($v['ballot_name'], $data['much_id'], $data['openid']);
                if ($check_option['status'] == 'error') {
                    return $this->json_rewrite($check_option);
                }
            }
        }
        $msg = '';
        $check_banned = Db::name('user_banned')->where('tory_id', $data['fa_class'])->where('user_id', $this->user_info['id'])->where('much_id', $data['much_id'])->find();
        if ($check_banned['refer_time'] > time()) {
            return $this->json_rewrite(['status' => 'error', 'id' => 0, 'msg' => '您已被禁言，解除时间:' . date('Y-m-d H:i:s', $check_banned['refer_time'])]);
        }

        //是否是付费贴
        if ($data['money_paper'] == 1) {
            if ($this->paper_smingle['buy_paper_number_limit'] != 0) {//判断是否有限制发帖
                $check_today = Db::name('paper')->where('much_id', $data['much_id'])->where('user_id', $this->user_info['id'])->where('is_buy', '1')->whereTime('adapter_time', 'today')->count();
                if ($check_today >= $this->paper_smingle['buy_paper_number_limit']) {
                    return $this->json_rewrite(['status' => 'error', 'id' => 0, 'msg' => '今日付费贴已达上限！']);
                }
            }
        } else {
            if ($this->paper_smingle['number_limit'] != 0) {//判断是否有限制发帖
                //今日发帖数量
                $check_today = Db::name('paper')->where('much_id', $data['much_id'])->where('user_id', $this->user_info['id'])->whereTime('adapter_time', 'today')->count();
                if ($check_today >= $this->paper_smingle['number_limit']) {
                    return $this->json_rewrite(['status' => 'error', 'id' => 0, 'msg' => '今日发帖已达上限！']);
                }
            }
        }
        if ($data['type'] == 3) {
            if (strtotime($data['active_star_time']) > strtotime($data['active_end_time'])) {
                return $this->json_rewrite(['status' => 'error', 'id' => 0, 'msg' => '活动时间错误，请重新选择！']);
            }
        }
        //$plug
        $renzheng = $plug->check_plug('eb677b7f-7227-0ffa-c31b-81ef2834a3c3', $data['much_id']);

        if ($renzheng) {
            $renzheng_check = $util->get_att_info($this->user_info['id'], $data['much_id']);
            if (empty($renzheng_check)) {
                return $this->json_rewrite(['status' => 'error', 'id' => 0, 'msg' => '请认证后发布内容！']);
            }
        }
        //判断身份铭牌
        if (isset($data['name_card']) && intval($data['name_card']) != 0) {
            //判断当前是否是否过期
            $check_name_card = Db::name('user_camouflage_card')
                ->where('ccid', $data['name_card'])
                ->where('user_id', $this->user_info['id'])
                ->where('much_id', $data['much_id'])
                ->where('expired_time', '> time', date('Y-m-d H:i:s', time()))
                ->find();
            if (empty($check_name_card)) {
                return $this->json_rewrite(['status' => 'name_error', 'id' => 0, 'msg' => '身份铭牌已过期！']);
            } else {
                $paper['uccid'] = $data['name_card'];
            }
        } else {
            $paper['uccid'] = 0;
        }
        if ($authority['engrave_arbor'] == 0) {
            $paper['uccid'] = 0;
        }

        $paper['img_show_type'] = isset($data['img_show_type']) ? $data['img_show_type'] : 0;
        $paper['study_video_bulk'] = $data['vd_width'] . ',' . $data['vd_height'];
        $paper['study_type'] = $data['type'];//0:文字1：语音2：视频3：活动
        $paper['user_id'] = $this->user_info['id'];
        $paper['tory_id'] = $data['fa_class'];
        $paper['study_title'] = $this->safe_html(emoji_encode($data['title']));
        $paper['study_title_color'] = $data['color'];
        $paper['adapter_time'] = time();
        $paper['is_open'] = $data['is_open'];
        $paper['much_id'] = $data['much_id'];
        $paper['tg_id'] = empty($data['gambit_id']) ? 0 : $data['gambit_id'];
        $open_file = Db::name('outlying')->where('much_id', $data['much_id'])->find();
        $img_hou = '';
        if ($open_file['quicken_type'] == 2) {
            $img_yun = json_decode($open_file['qiniu_follow'], true);
            if (!empty($img_yun['qiniu_watermark'])) {
                $img_hou = '?' . authcode($img_yun['qiniu_watermark'], 'DECODE', 'YuluoNetwork', 0);
            }
        }

        $data['content'] = $this->safe_html($data['content']);
        /**
         * 第一个版本低于第二个版本的时候 return -1
         * 第一个版本等于第二个版本的时候 return 0
         * 第一个版本高于第二个版本的时候 return 1
         */
        if (version_compare($data['version'], '1.0.40') != -1) {
            //return $this->json_rewrite($data['get_content']);
            //获取图文
            $get_content = json_decode($data['get_content'], true);
            $sql_content = '';
            if (isset($data['img_show_type']) && $data['img_show_type'] == 1) {
                //组合html
                $data['img_arr'] = json_decode($data['img_arr'], true);
                foreach ($data['img_arr'] as $k => $v) {
                    $sql_content .= "<div><p><img src='{$v}{$img_hou}' ></p></div>";
                }

                //组合html
                foreach ($get_content as $k => $v) {
                    if ($v['type'] == 'text' && !empty(preg_replace('# #', '', $v['value']))) {
                        $check_text = $util->get_check_msg($v['value'], $data['much_id'], $data['openid']);
                        if ($check_text['status'] == 'error') {
                            return $this->json_rewrite($check_text);
                        }
                        $value = emoji_encode(nl2br($v['value']));

                        if ($v['hide'] == 1 && $data['money_paper'] != 1) {
                            //$sql_content .= "<p class='stealth_module'>{$value}</p>";
                            $sql_content .= "<div class=\"stealth_module\"><p>{$value}</p></div>";
                        } else {
                            $sql_content .= "<div><p>{$value}</p></div>";

                        }
                    }

                }
            } else {
                //组合html
                foreach ($get_content as $k => $v) {
                    if ($v['type'] == 'img' && !empty(preg_replace('# #', '', $v['value']))) {
                        $value = $v['value'] . $img_hou;
                        if ($v['hide'] == 1 && $data['money_paper'] != 1) {
                            $sql_content .= "<div class=\"stealth_module\"><p><img src='{$value}' ></p></div>";
                        } else {
                            $sql_content .= "<div><p><img src='{$value}' ></p></div>";

                        }

                    }
                    if ($v['type'] == 'text' && !empty(preg_replace('# #', '', $v['value']))) {
                        $check_text = $util->get_check_msg($v['value'], $data['much_id'], $data['openid']);
                        if ($check_text['status'] == 'error') {
                            return $this->json_rewrite($check_text);
                        }
                        $value = emoji_encode(nl2br($v['value']));
                        if ($v['hide'] == 1 && $data['money_paper'] != 1) {
                            //$sql_content .= "<p class='stealth_module'>{$value}</p>";
                            $sql_content .= "<div class=\"stealth_module\"><p>{$value}</p></div>";
                        } else {

                            $sql_content .= "<div><p>{$value}</p></div>";

                        }
                    }

                }
            }
            if ($data['type'] == 0 && empty($sql_content)) {
                $rs = ['status' => 'error', 'msg' => '内容不能为空'];
                return $this->json_rewrite($rs);
            }

            $paper['study_content'] = $sql_content;
            $paper['image_part'] = json_encode([]);
        } else {
            if (!empty($data['img_arr'])) {
                foreach ($data['img_arr'] as $k => $v) {
                    $data['img_arr'][$k] = $v . $img_hou;
                }
                $paper['image_part'] = json_encode([$data['img_arr']]);
            }
            $paper['study_content'] = emoji_decode($data['content']);
        }

        if (!isset($data['get_content'])) {

            $paper['study_content'] = emoji_decode($data['content']);
        }

        if ($plug->check_plug('c2b0e5ea-90e6-16af-7086-5e095954cf05', $data['much_id'])) {
            $call_config = Db::name('call_phone_config')->where('much_id', $data['much_id'])->find();
            $phone_config = empty($call_config) ? 0 : $call_config['force_input_phone'];
            if ($phone_config == 1 && empty($data['my_phone'])) {
                return $this->json_rewrite(['status' => 'error', 'msg' => '手机号不能为空']);
            }
            //判断一键拨号
            if ($data['open_phone'] == 1 && !empty($data['my_phone'])) {
                $paper['call_phone'] = $data['my_phone'];
            }
        }

        //判断网盘
        if (!$plug->check_plug('2d08651c-15b9-1924-c83e-6c65680a82be', $data['much_id'])) {
            $data['file_id'] = 0;
        }
        //return $this->json_rewrite($paper);
        //订阅 是否付费帖 0.否 1.文字 2.附件 3.文字+附件
        $is_buy = 0;
        //帖子设置了 订阅 但是没有网盘文件
        if ($data['money_paper'] == 1 && $data['file_id'] == 0) {
            $is_buy = 1;
        }
        //网盘文件+订阅+文件售价
        if ($data['file_id'] != 0 && $data['money_paper'] == 1 && $data['file_off_money'] == 1) {
            $is_buy = 2;
        }
        //网盘文件+订阅
        if ($data['file_id'] != 0 && $data['money_paper'] == 1 && $data['file_off_money'] == 0) {
            $is_buy = 3;
        }
        if (abs($data['zong_paper_money']) == 0) {
            $is_buy = 0;
        }
        if ($is_buy != 0) {
            $paper['is_buy'] = $is_buy;
            $paper['buy_price_type'] = $data['ding_yue_type'];
            $paper['buy_price'] = abs($data['zong_paper_money']);
            $paper['study_status'] = $this->paper_smingle['buy_paper_auto_review'];//付费贴审核状态
        } else {
            $paper['study_status'] = $this->paper_smingle['auto_review'];//普通审核状态
            $paper['buy_price_type'] = 0;
        }
        //审核时间设置
        if ($data['money_paper'] == 1) {
            if ($this->paper_smingle['buy_paper_auto_review'] == 1) {
                $paper['prove_time'] = time();
            } else {
                $paper['prove_time'] = 0;
            }
        } else {
            if ($this->paper_smingle['auto_review'] == 1) {
                $paper['prove_time'] = time();
            } else {
                $paper['prove_time'] = 0;
            }
        }


        if ($data['type'] == 1) {
            $paper['study_voice'] = $data['user_file'];
            $paper['study_voice_time'] = $data['file_ss'];
        } else if ($data['type'] == 2) {
            //获取当前开启的附件
            $fmppg = '';
            //阿里
//            if ($open_file['quicken_type'] == 1) {
//                $fmppg = '?x-oss-process=video/snapshot,t_3000,f_png';
//
//                $paper['image_part'] = json_encode([$data['user_file'] . $fmppg]);
//            }
//            //七牛
//            if ($open_file['quicken_type'] == 2) {
//                $fmppg = '?vframe/png/offset/1';
//                $paper['image_part'] = json_encode([$data['user_file'] . $fmppg]);
//            }
            $paper['study_video'] = $data['user_file'];
            if (!empty($data['top_img_arr'])) {

                $data['top_img_arr'] = $data['top_img_arr'] . $img_hou;

                $paper['image_part'] = json_encode([$data['top_img_arr']]);
            }
        } else if ($data['type'] == 0) {
            $paper['study_voice'] = '';
            $paper['study_video'] = '';
        }
        //判断解析
        if ($plug->check_plug('203421c1-ebc5-d393-96a4-424251758adb', $data['much_id'])) {
            if ($data['is_jiexi'] == 1) {
                $paper['video_type'] = 2;
                $str_r = '/(http:\/\/|https:\/\/)((\w|=|\?|\.|\/|&|-)+)/';
                preg_match_all($str_r, $data['jiexi_text'], $arr);
                $share_url = $arr[0][0];
                $paper['third_part_vid'] = $share_url;
                $paper['study_video'] = '';
            }

        }
        $paper['address_name'] = $data['position_name'] == '' ? NULL : $data['position_name'];
        $paper['address_details'] = $data['position'] == '' ? NULL : $data['position'];

        $paper['address_latitude'] = $data['address_latitude'] == '' ? NULL : $data['address_latitude'];
        $paper['address_longitude'] = $data['address_longitude'] == '' ? NULL : $data['address_longitude'];
        $paper['vote_deadline'] = $data['option_end_time'] == 0 ? 0 : strtotime($data['option_end_time']);;
        // 启动事务
        Db::startTrans();
        try {
            $res = Db::name('paper')->insertGetId($paper);
            if (!$res) {
                Db::rollback();
                return $this->json_rewrite(['status' => 'error', 'id' => 0, 'msg' => '发布失败，请稍候重试-1！']);
            }
            //如果是视频号
            if($data['type']==6){
                $paper_wechat_channel_video = [
                    'paper_id'=>$res,
                    'feed_token'=>$data['feedToken'],
                    'create_time'=>time(),
                    'much_id'=>$data['much_id']
                ];
                $sph_res = Db::name('paper_wechat_channel_video')->insert($paper_wechat_channel_video);
                if (!$sph_res) {
                    Db::rollback();
                    return $this->json_rewrite(['status' => 'error', 'id' => 0, 'msg' => '发布失败，请稍候重试-sp！']);
                }
            }
            //如果有文件
            if ($data['file_id'] != 0) {
                $belong = Db::name('netdisc_belong')
                    ->where('much_id', $data['much_id'])
                    ->where('id', $data['file_id'])
                    ->find();
                if ($belong['is_sell'] == 0 && ($this->user_info['id'] != $belong['user_id'])) {
                    return $this->json_rewrite(['status' => 'error', 'id' => 0, 'msg' => '发布失败，文件不属于您！']);
                }
                if ($belong['is_del'] == 1) {
                    return $this->json_rewrite(['status' => 'error', 'id' => 0, 'msg' => '发布失败，文件已被删除！']);
                }
                if ($belong['is_dir'] == 0) {
                    $netdisc = Db::name('netdisc')
                        ->where('much_id', $data['much_id'])
                        ->where('id', $belong['nc_id'])
                        ->find();
                    if ($netdisc['file_status'] == 0) {
                        return $this->json_rewrite(['status' => 'error', 'id' => 0, 'msg' => '发布失败，文件违规已被屏蔽！']);
                    }
                }
                $file = Db::name('netdisc_sell')->insert(['pa_id' => $res, 'nc_id' => $belong['nc_id'], 'nb_id' => $data['file_id'], 'is_sell' => $data['file_off_fowd'], 'much_id' => $data['much_id']]);
                if (!$file) {
                    Db::rollback();
                    return $this->json_rewrite(['status' => 'error', 'id' => 0, 'msg' => '发布失败，请稍候重试-8！']);
                }
            }

            if ($data['type'] == 4 || $data['type'] == 5) {
                foreach ($option as $k => $v) {
                    $option[$k]['paper_id'] = $res;
                    $option[$k]['much_id'] = $data['much_id'];
                }
                $vo_ins = Db::name('paper_vote')->insertAll($option);
                if (!$vo_ins) {
                    Db::rollback();
                    return $this->json_rewrite(['status' => 'error', 'id' => 0, 'msg' => '发布失败，请稍候重试-2！']);
                }
            }
            //如果是活动贴
            if ($data['type'] == 3) {
                if(!is_int(intval($data['active_num']))){
                    Db::rollback();
                    return $this->json_rewrite(['status' => 'error', 'id' => 0, 'msg' => '参与人数需是整数！']);
                }
                $at['is_approve'] = 0;
                $at['paper_id'] = $res;
                $at['brisk_address'] = $data['active_address'];
                $at['brisk_address_latitude'] = $data['active_latitude'];
                $at['brisk_address_longitude'] = $data['active_longitude'];
                $at['start_time'] = strtotime($data['active_star_time']);
                $at['end_time'] = strtotime($data['active_end_time']);
                $at['number_of_people'] = $data['active_num'];
                $at['much_id'] = $data['much_id'];
                $at_ins = Db::name('brisk_team')->insert($at);
                if (!$at_ins) {
                    Db::rollback();
                    return $this->json_rewrite(['status' => 'error', 'id' => 0, 'msg' => '发布失败，请稍候重试-3！']);
                }
            }
            //如果是投票贴 4单 5多
            //如果开启红包
            if ($data['red_paper'] == 1) {
                //判断用户积分
                if ($data['fu_dai_type'] == 0 && ($this->user_info['conch'] < abs($data['zong_red_money']))) {//贝壳
                    Db::rollback();
                    return $this->json_rewrite(['status' => 'error', 'id' => 0, 'msg' => '发布失败，' . $this->design['currency'] . '不足！']);
                }
                //判断用户积分
                if ($data['fu_dai_type'] == 1 && $this->user_info['fraction'] < abs($data['zong_red_money'])) {//积分
                    Db::rollback();
                    return $this->json_rewrite(['status' => 'error', 'id' => 0, 'msg' => '发布失败，' . $this->design['confer'] . '不足！']);
                }
                //增加红包数据
                $red['paper_id'] = $res;
                if ($data['fu_dai_type'] == 0) {
                    $red['initial_conch'] = $data['zong_red_money'];
                    $red['surplus_conch'] = $data['zong_red_money'];
                    $red['initial_fraction'] = 0;
                    $red['surplus_fraction'] = 0;
                } else {
                    $red['initial_conch'] = 0;
                    $red['surplus_conch'] = 0;
                    $red['initial_fraction'] = $data['zong_red_money'];
                    $red['surplus_fraction'] = $data['zong_red_money'];
                }
                $red['initial_quantity'] = $data['zong_red_count'];
                $red['surplus_quantity'] = $data['zong_red_count'];
                $red['red_type'] = $data['red_type'];
                $red['much_id'] = $data['much_id'];
                $red['initial_type'] = empty($data['fu_dai_type']) ? 0 : $data['fu_dai_type'];
                $red_res = Db::name('paper_red_packet')->insert($red);
                if (!$red_res) {
                    Db::rollback();
                    return $this->json_rewrite(['status' => 'error', 'id' => 0, 'msg' => '发布失败，请稍候重试！1']);
                }
                //贝壳明细表增加数据
                $amount['user_id'] = $this->user_info['id'];
                $amount['category'] = 2;
                $amount['finance'] = -$data['zong_red_money'];
                if ($data['fu_dai_type'] == 0) {
                    //积分
                    $amount['poem_fraction'] = $this->user_info['fraction'];
                    $amount['surplus_fraction'] = $this->user_info['fraction'];
                    //贝壳
                    $amount['poem_conch'] = $this->user_info['conch'];
                    $amount['surplus_conch'] = bcsub($this->user_info['conch'], $data['zong_red_money'], 2);
                    $money = ['conch' => $amount['surplus_conch']];
                } else {
                    //积分
                    $amount['poem_fraction'] = $this->user_info['fraction'];
                    $amount['surplus_fraction'] = bcsub($this->user_info['fraction'], $data['zong_red_money'], 2);
                    //贝壳
                    $amount['poem_conch'] = $this->user_info['conch'];
                    $amount['surplus_conch'] = $this->user_info['conch'];
                    $money = ['fraction' => $amount['surplus_fraction']];
                }

                $amount['ruins_time'] = time();
                $amount['solution'] = '发布红包贴';
                $amount['evaluate'] = $data['fu_dai_type'];
                $amount['much_id'] = $data['much_id'];
                $amount_res = Db::name('user_amount')->insert($amount);
                if (!$amount_res) {
                    Db::rollback();
                    return $this->json_rewrite(['status' => 'error', 'id' => 0, 'msg' => '发布失败，请稍候重试！2']);
                }
                //减少用户积分
                $user_res = Db::name('user')->where('id', $this->user_info['id'])->where('much_id', $data['much_id'])->update($money);
                if (!$user_res) {
                    Db::rollback();
                    return $this->json_rewrite(['status' => 'error', 'id' => 0, 'msg' => '发布失败，请稍候重试！3']);
                }
            }
            $mb = 0;
            if ($this->paper_smingle['auto_review'] == 0 && $data['money_paper'] == 0) {
                $msg = '请等待审核！';
                Db::name('prompt_msg')->insert(['capriole' => 4, 'tyid' => 0, 'msg_time' => time(), 'type' => 0, 'retter' => '用户：' . $this->user_info['user_nick_name'] . '发布了一条帖子待审核！', 'status' => 0, 'much_id' => $data['much_id']]);
                $notices = Db::name('prompt_msg')
                    ->where('status', 0)
                    ->where('type', 0)
                    ->where('much_id', $data['much_id'])
                    ->count('*');
                cache('notices_' . $data['much_id'], $notices);
                $mb = 1;
            }
            if ($this->paper_smingle['buy_paper_auto_review'] == 0 && $data['money_paper'] == 1) {
                $msg = '请等待审核！';
                Db::name('prompt_msg')->insert(['capriole' => 4, 'tyid' => 0, 'msg_time' => time(), 'type' => 0, 'retter' => '用户：' . $this->user_info['user_nick_name'] . '发布了一条帖子待审核！', 'status' => 0, 'much_id' => $data['much_id']]);
                $notices = Db::name('prompt_msg')
                    ->where('status', 0)
                    ->where('type', 0)
                    ->where('much_id', $data['much_id'])
                    ->count('*');
                cache('notices_' . $data['much_id'], $notices);

                $mb = 1;
            }
            if ($mb == 1) {
                //查询open_id
                $user_maker = Db::name('user_maker')->orderRaw('rand()')->where('much_id', $data['much_id'])->where('status', 1)->find();
                $user_mb = Db::name('user')->where('user_wechat_open_id', $user_maker['user_open_id'])->where('much_id', $data['much_id'])->where('status', 1)->find();
                if (!empty($user_mb)) {
                    //模板消息
                    $tmplData = [
                        'much_id' => $data['much_id'],
                        'at_id' => 'YL0099',
                        'user_id' => $user_mb['id'],
                        'page' => 'yl_welore/pages/packageC/examine/index',
                        'keyword1' => emoji_decode($this->user_info['user_nick_name']) . '发布了一条帖子待审核!',
                        'keyword2' => date('Y年m月d日 H:i:s', time())
                    ];
                    $tmplService = new TmplService();
                    $tmplService->add_template($tmplData);
                }
            }
            if ($this->paper_smingle['buy_paper_auto_review'] == 1 && $data['money_paper'] == 1) {
                $msg = '发布成功！';
            }
            if ($this->paper_smingle['buy_paper_auto_review'] == 1 && $this->paper_smingle['auto_review'] == 1) {
                $msg = '发布成功！';
            }
            if ($this->paper_smingle['auto_review'] == 1 && $data['money_paper'] == 0) {
                $msg = '发布成功！';
            }
            // 新人营销
            if ($this->paper_smingle['auto_review'] == 1) {
                $task = $util->new_user_task(['uid' => $this->user_info['id'], 'key' => 1, 'tory_id' => $paper['tory_id'], 'paper_id' => $res, 'much_id' => $data['much_id']]);
                if ($task['code'] == 0) {
                    $msg = $task['msg'];
                } else if ($task['code'] == 1) {
                    Db::rollback();
                    return $this->json_rewrite(['status' => 'error', 'id' => 0, 'msg' => '发布失败，请稍候重试！4']);
                }
            }
            Db::commit();
            Cache::clear("globalIndexCache_".$data['much_id']);
            return $this->json_rewrite(['status' => 'success', 'id' => $res, 'msg' => $msg]);
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            return $this->json_rewrite(['status' => 'error', 'id' => 0, 'msg' => '发布失败，请稍候重试！' . $e->getMessage()]);
        }
    }

    /**
     * 收藏帖子
     */
    public function add_user_collect()
    {
        $rs = ['status' => 'success', 'msg' => '收藏成功！'];
        $data = input('param.');
        if ($data['sc_type'] == 1) {
            $ins = Db::name('user_collect')->where('much_id', $data['much_id'])->where('user_id', $this->user_info['id'])->where('paper_id', $data['id'])->delete();
            if ($ins) {
                $rs['msg'] = '取消收藏成功！';
            } else {
                $rs['status'] = 'error';
                $rs['msg'] = '取消收藏失败！';
            }
            $count = Db::name('user_collect')->where('much_id', $data['much_id'])->where('paper_id', $data['id'])->count();
            $rs['info_sc_count'] = $count;
            $rs['info_sc'] = false;
            return $this->json_rewrite($rs);
        }
        $ins_data['user_id'] = $this->user_info['id'];
        $ins_data['paper_id'] = $data['id'];
        $ins_data['much_id'] = $data['much_id'];
        $ins_data['create_time'] = time();
        $ins = Db::name('user_collect')->insert($ins_data);
        $count = Db::name('user_collect')->where('much_id', $data['much_id'])->where('paper_id', $data['id'])->count();
        if (!$ins) {
            $rs['status'] = 'error';
            $rs['msg'] = '收藏失败！';
        }
        $rs['info_sc_count'] = $count;
        $rs['info_sc'] = true;


        //用户详情
        $user_info = Db::name('user')->where('id', $this->user_info['id'])->where('much_id', $data['much_id'])->find();
        //帖子详情
        $util = new Util();
        $fa_info = $util->get_page_user($data['id']);

        $page_title = $fa_info['study_title'] == '' ? subtext($fa_info['study_content'], 10) : subtext($fa_info['study_title'], 10);
        $page_title = strip_tags(emoji_decode($page_title));
        if (empty($page_title)) {
            if ($fa_info['study_type'] == 0) {
                $page_title = '[图片帖子]';
            }
            if ($fa_info['study_type'] == 1) {
                $page_title = '[语音帖子]';
            }
            if ($fa_info['study_type'] == 2) {
                $page_title = '[视频帖子]';
            }
        }
        //发送模版
        $util->add_template(['much_id' => $data['much_id'],
            'at_id' => 'YL0003',
            'user_id' => $fa_info['user_id'],
            'page' => 'yl_welore/pages/packageA/article/index?id=' . $fa_info['id'] . '&type=' . $fa_info['study_type'],
            'keyword1' => $page_title,
            'keyword2' => emoji_decode($user_info['user_nick_name']),
            'keyword3' => date('Y-m-d H:i', time()),
        ]);

        return $this->json_rewrite($rs);
    }

    /**
     * 获取收藏帖子
     */
    public function get_user_collection()
    {
        $rs = ['status' => 'success', 'msg' => '获取成功'];
        $data = input('param.');
        $where = [];
        $page = $data['page'];

        if ($this->version == 1) {
            $where['p.study_type'] = ['in', ['0', '1']];
        }

        $list = Db::name('user_collect')
            ->alias('c')
            ->join('paper p', 'p.id=c.paper_id')
            ->join('user u', 'p.user_id=u.id')
            ->join('territory t', 'p.tory_id=t.id')
            ->where('p.whether_delete', '0')
            ->where('c.much_id', $data['much_id'])
            ->where('c.user_id', $this->user_info['id'])
            ->where($where)
            ->field('p.*,u.gender,u.level,u.wear_merit,u.user_nick_name,u.user_head_sculpture,t.realm_name,t.realm_icon')
            ->order('c.create_time', 'desc')
            ->page($page, '15')
            ->select();
        // return $this->json_rewrite($list);
        if ($list) {
            foreach ($list as $k => $v) {

                //计算全部回复人数
                $huifu_all_count = Db::name('paper_reply')->where('paper_id', $v['id'])->where('whether_delete=0')->where('much_id', $data['much_id'])->count();

                $huifu_hui_count = Db::name('paper_reply')->alias('p')
                    ->join('paper_reply_duplex r', 'r.reply_id=p.id')
                    ->where('p.whether_delete=0')
                    ->where('p.much_id', $data['much_id'])
                    ->where('p.paper_id', $v['id'])
                    ->count();

                $list[$k]['is_voice'] = false;
                $list[$k]['user_nick_name'] = emoji_decode($v['user_nick_name']);
                $list[$k]['study_title'] = emoji_decode($v['study_title']);
                $list[$k]['study_content'] = strip_tags(emoji_decode($v['study_content']));

                $preg = '/<img.*?src=[\"|\'](.*?)[\"|\'].*?>/i';
                preg_match_all($preg, emoji_decode($v['study_content']), $match);

                if (!empty(json_decode($v['image_part'], true))) {
                    $list[$k]['image_part'] = json_decode($v['image_part']);
                } else {
                    $list[$k]['image_part_2'] = $match;
                    if (!empty($match[0])) {
                        $list[$k]['image_part'] = $match[1];
                    } else {
                        $list[$k]['image_part'] = [];
                    }
                }
                $util = new Util();
                $list[$k]['study_heat'] = formatNumber($v['study_heat']);
                $list[$k]['study_laud'] = formatNumber($v['study_laud']);
                $list[$k]['study_repount'] = formatNumber($huifu_hui_count + $huifu_all_count);
                $list[$k]['user_vip'] = $util->get_user_vip($v['user_id']);
                $list[$k]['adapter_time'] = formatTime($v['adapter_time']);

                $list[$k]['study_voice_time'] = s_to_hs($v['study_voice_time']);
                $list[$k]['starttime'] = '00:00';
                $list[$k]['adapter_time'] = formatTime($v['adapter_time']);
                $list[$k]['offset'] = 0;
                $list[$k]['max'] = $v['study_voice_time'];

                $list[$k]['study_content'] = Alternative::ExpressionHtml($list[$k]['study_content']);

                $sc = Db::name('user_applaud')->where('much_id', $data['much_id'])->where('user_id', $this->user_info['id'])->where('paper_id', $v['id'])->count();
                $list[$k]['is_info_zan'] = $sc == 0 ? false : true;
                $count = $v['study_laud'];
                $list[$k]['info_zan_count'] = formatNumber($count);
                $list[$k]['info_zan_count_this'] = $count;
                //查询当前帖子是否是红包贴
                $red = Db::name('paper_red_packet')->where('paper_id', $v['id'])->where('much_id', $data['much_id'])->count();
                $list[$k]['red'] = $red;
                $list[$k]['level'] = $util->get_user_level($v['level'], $data['much_id'])['level_icon'];
                //当前用户的勋章
                $list[$k]['wear_merit'] = $util->get_medal($v['wear_merit'], $data['much_id']);
                //是否是投票贴
                $list[$k]['vo_id'] = [];
                //查询我是否投票了
                $list[$k]['is_vo_check'] = Db::name('user_vote')->where('user_id', $this->user_info['id'])->where('paper_id', $v['id'])->where('much_id', $data['much_id'])->count();
                if ($list[$k]['is_vo_check'] > 0) {
                    $my_vo = Db::name('user_vote')->where('paper_id', $v['id'])->where('user_id', $this->user_info['id'])->where('much_id', $data['much_id'])->select();
                    foreach ($my_vo as $c => $d) {
                        array_push($list[$k]['vo_id'], intval($d['pv_id']));
                    }
                }
                $pvInfo = Db::name('paper_vote')->where('paper_id', $v['id'])->where('much_id', $data['much_id'])->select();
                $pvInfoSum = Db::name('paper_vote')->where('paper_id', $v['id'])->where('much_id', $data['much_id'])->sum('cheat_ballot');
                $pvPeopleCount = Db::name('user_vote')->where('paper_id', $v['id'])->where('much_id', $data['much_id'])->count();
                $pvPeopleCountTotal = ($pvInfoSum + $pvPeopleCount);
                $list[$k]['vo_count'] = $pvPeopleCountTotal;
                for ($i = 0; $i < count($pvInfo); $i++) {
                    $voters = Db::name('user_vote')->where('paper_id', $v['id'])->where('pv_id', $pvInfo[$i]['id'])->where('much_id', $data['much_id'])->count();
                    $pvInfo[$i]['voters'] = (intval($pvInfo[$i]['cheat_ballot']) + $voters);
                    // PHP 8.4兼容性修复：防止除零错误
                    if ($pvPeopleCountTotal > 0) {
                        $ratio = (($pvInfo[$i]['voters'] / $pvPeopleCountTotal) * 100);
                        if (is_nan($ratio)) {
                            $pvInfo[$i]['ratio'] = 0;
                        } else {
                            $pvInfo[$i]['ratio'] = $ratio;
                        }
                    } else {
                        // 当总投票数为0时，设置比例为0
                        $pvInfo[$i]['ratio'] = 0;
                    }
                }
                $list[$k]['vo'] = $pvInfo;
                //判断是否有身份铭牌
                if (intval($v['uccid']) != 0) {
                    //查询佩戴的身份
                    $card_info = Db::name('camouflage_card')->where('id', $v['uccid'])->where('much_id', $data['much_id'])->find();
                    $user_card = Db::name('user_camouflage_card')->where('ccid', $v['uccid'])->where('user_id', $v['user_id'])->order('expired_time desc')->where('much_id', $data['much_id'])->find();
                    $list[$k]['user_head_sculpture'] = $card_info['forgery_head'];
                    $list[$k]['user_id'] = 0;
                    //$list[$k]['user_nick_name'] = $card_info['forgery_name'] . '-' . $user_card['id'];
                    $list[$k]['user_nick_name'] = $card_info['forgery_name'];
                }

            }
            $rs['info'] = $list;
        } else {
            $rs['info'] = [];
        }
        $rs['version'] = $this->version;
        return $this->json_rewrite($rs);
    }

    /**
     * 举报帖子
     */
    public function add_paper_complaint()
    {
        $rs = ['status' => 'success', 'msg' => '举报成功！'];
        $data = input('param.');

        $cheek = Db::name('paper_complaint')->where('tale_type', 0)->where('acceptance_status', 0)->where('user_id', $this->user_info['id'])->where('paper_id', $data['id'])->find();
        $cheek_1 = Db::name('paper_complaint')->where('tale_type', 1)->where('acceptance_status', 0)->where('user_id', $this->user_info['id'])->where('prely_id', $data['id'])->find();
        if ($data['tale_type'] == 0) {//帖子
            if ($cheek) {
                $rs = ['status' => 'error', 'msg' => '您已举报，请等待处理！'];
                return $this->json_rewrite($rs);
            }
        }
        if ($data['tale_type'] == 1) {//回复
            if ($cheek_1) {
                $rs = ['status' => 'error', 'msg' => '您已举报，请等待处理！'];
                return $this->json_rewrite($rs);
            }
        }


        $ins['user_id'] = $this->user_info['id'];
        $ins['tale_type'] = $data['tale_type'];
        $util = new Util();
        if ($data['tale_type'] == 0) {
            $ins['paper_id'] = $data['id'];
            $ins['tory_id'] = $util->get_page_user($data['id'])['tory_id'];
        }
        if ($data['tale_type'] == 1) {

            $ins['prely_id'] = $data['id'];
            $ins['tory_id'] = $util->get_user_applaud($data['id'])['tory_id'];
        }

        $ins['tale_content'] = emoji_encode($data['content']);
        $ins['petition_time'] = time();
        $ins['much_id'] = $data['much_id'];
        $ins['is_strike'] = 0;
        $red = Db::name('paper_complaint')->insert($ins);
        if (!$red) {
            $rs['status'] = 'error';
            $rs['msg'] = '举报失败，请稍候重试！';
        }
        $type = $data['tale_type'] == 0 ? '帖子' : '回复';
        $rett = "用户" . $this->user_info['user_nick_name'] . "举报了一个{$type}，请及时处理！";
        Db::name('prompt_msg')->insert(['capriole' => 6, 'tyid' => 0, 'msg_time' => time(), 'type' => 1, 'retter' => $rett, 'status' => 0, 'much_id' => $data['much_id']]);
        $notices = Db::name('prompt_msg')
            ->where('status', 0)
            ->where('type', 1)
            ->where('much_id', $data['much_id'])
            ->count('*');
        cache('vacants_' . $data['much_id'], $notices);
        return $this->json_rewrite($rs);
    }


    /**
     * 回复点赞
     */
    public function add_paper_prely()
    {
        $rs = ['status' => 'success', 'msg' => '点赞成功！'];
        $data = input('param.');
        $check = Db::name('user_applaud')->where('paper_id', $data['hui_id'])->where('user_id', $this->user_info['id'])->where('applaud_type', 1)->find();
        if ($check) {
            Db::name('user_applaud')->where('paper_id', $data['hui_id'])->where('user_id', $this->user_info['id'])->where('applaud_type', 1)->delete();
            $rs = ['status' => 'success', 'msg' => '成功！'];
            Db::name('paper_reply')->where('id', $data['hui_id'])->setDec('praise');
            return $this->json_rewrite($rs);
        }
        $ins['paper_id'] = $data['hui_id'];//回复id
        $ins['user_id'] = $this->user_info['id'];
        $ins['applaud_type'] = 1;
        $ins['laud_time'] = time();
        $ins['much_id'] = $data['much_id'];

        $red = Db::name('user_applaud')->insert($ins);
        if (!$red) {
            $rs = ['status' => 'error', 'msg' => '点赞失败！'];
            return $this->json_rewrite($rs);
        }
        Db::name('paper_reply')->where('id', $data['hui_id'])->setInc('praise');
        return $this->json_rewrite($rs);
    }

    /**
     * 删除帖子
     */
    public function del_article()
    {

        $data = input('param.');

        $info = Db::name('paper')->where('id', $data['paper_id'])->find();
        if ($info['whether_delete'] == 1) {
            $rs = ['status' => 'success', 'msg' => '删除成功！'];
            return $this->json_rewrite($rs);
        }
        $up = [];
        if ($this->user_info['id'] == $info['user_id']) {
            $up['whetd_time'] = time();
            $up['whether_delete'] = 1;
            $up['whether_type'] = 3;
            $up['whether_reason'] = '用户自己删除';
            $up['token'] = md5(time());
        }
        if (!empty($data['is_qq_text'])) {
            $up['whetd_time'] = time();
            $up['whether_delete'] = 1;
            $up['whether_type'] = 2;
            $up['whether_reason'] = emoji_encode($data['is_qq_text']);
            $up['token'] = md5(time());
        }
        $util = new Util();
        $del = Db::name('paper')->where('much_id', $data['much_id'])->where('id', $data['paper_id'])->update($up);
        if ($del) {
            $rs = ['status' => 'success', 'msg' => '删除成功！'];
            if (!empty($data['is_qq_text'])) {
                $tatle = strip_tags(emoji_decode($info['study_title'] == '' ? $info['study_content'] : $info['study_title']));
                $msg = "您的帖子【" . subtext($tatle, 20) . "】由于：" . emoji_encode($data['is_qq_text']) . "；被管理员删除，如有疑问请到服务中心申诉";
                $util->add_user_smail($info['user_id'], $msg, $data['much_id'], '5', '0');
            }
        } else {
            $rs = ['status' => 'error', 'msg' => '删除失败！'];
        }
        Cache::clear("globalIndexCache_".$data['much_id']);
        return $this->json_rewrite($rs);
    }

    /**
     * 删除回复
     */
    public function del_article_huifu()
    {
        $rs = ['status' => 'success', 'msg' => '删除成功！'];
        $data = input('param.');
        $util = new Util();
        if($data['del_type']==1){
            $check = $util->get_user_applaud($data['id']);
            $huifu_info = Db::name('paper_reply')->where('id', $data['id'])->find();
        }else{
            $c= Db::name('paper_reply_duplex')
                ->where('id',$data['id'])
                ->find();
            $p= Db::name('paper_reply')
                ->where('id',$c['reply_id'])
                ->find();
            $check = $util->get_user_applaud($p['id']);
            $huifu_info = Db::name('paper_reply_duplex')->where('id', $data['id'])->find();
        }

        if ($this->user_info['id'] == $check['user_id']) {
            $up['whetd_time'] = time();
            $up['whether_delete'] = 1;
            $up['whether_type'] = 4;
            $up['whether_reason'] = '楼主删除';
            $up['token'] = md5(time());
        }
        if ($this->user_info['id'] == $huifu_info['user_id']) {
            $up['whetd_time'] = time();
            $up['whether_delete'] = 1;
            $up['whether_type'] = 3;
            $up['whether_reason'] = '用户自己删除';
            $up['token'] = md5(time());
        }
        if (!empty($data['is_qq_text'])) {
            $up['whetd_time'] = time();
            $up['whether_delete'] = 1;
            $up['whether_type'] = 2;
            $up['whether_reason'] = emoji_encode($data['is_qq_text']);
            $up['token'] = md5(time());
        }
        if($data['del_type']==1){
            $del = Db::name('paper_reply')->where('id', $data['id'])->update($up);
        }else{
            unset($up['whether_type']);
            unset($up['token']);
            $del = Db::name('paper_reply_duplex')->where('id', $data['id'])->update($up);
        }

        if (!$del) {
            $rs['status'] = 'error';
            $rs['msg'] = '删除失败！';
        }
        Db::name('paper')->where('id', $data['paper_id'])->setDec('study_repount');

        if (!empty($data['is_qq_text'])) {
            $msg = '您的回复由于：' . emoji_encode($data['is_qq_text']) . "被管理员删除，如有疑问请到服务中心申诉";
            $util->add_user_smail($huifu_info['user_id'], $msg, $data['much_id'], '4', '0');
        }

        return $this->json_rewrite($rs);
    }

    /**
     * 赞帖子
     */
    public function add_user_zan()
    {
        $rs = ['status' => 'success', 'msg' => '赞成功！'];
        $data = input('param.');
        if ($data['zan_type'] == 1) {
            Db::name('user_applaud')->where('applaud_type', $data['applaud_type'])->where('much_id', $data['much_id'])->where('user_id', $this->user_info['id'])->where('paper_id', $data['id'])->delete();
            $count = Db::name('user_applaud')->where('applaud_type', $data['applaud_type'])->where('much_id', $data['much_id'])->where('paper_id', $data['id'])->count();
            $rs['info_zan_count'] = formatNumber($count);
            $rs['info_zan'] = false;
            $rs['status'] = 'success';
            $rs['msg'] = '取消成功！';
            Db::name('paper')->where('id', $data['id'])->setDec('study_laud');
            return $this->json_rewrite($rs);
        }
        $check_count = Db::name('user_applaud')->where('applaud_type', $data['applaud_type'])->where('user_id', $this->user_info['id'])->where('much_id', $data['much_id'])->where('paper_id', $data['id'])->count();
        if ($check_count > 0) {
            $rs['info_zan'] = true;
            //Db::name('paper')->where('id', $data['id'])->setInc('study_laud');
            return $this->json_rewrite($rs);
        }
        $ins_data['user_id'] = $this->user_info['id'];
        $ins_data['paper_id'] = $data['id'];
        $ins_data['much_id'] = $data['much_id'];
        $ins_data['laud_time'] = time();
        $ins_data['applaud_type'] = $data['applaud_type'];
        Db::name('user_applaud')->insert($ins_data);
        $count = Db::name('user_applaud')->where('applaud_type', $data['applaud_type'])->where('much_id', $data['much_id'])->where('paper_id', $data['id'])->count();
        $rs['info_zan_count'] = formatNumber($count);
        $rs['info_zan'] = true;
        Db::name('paper')->where('id', $data['id'])->setInc('study_laud');

        //用户详情
        $user_info = Db::name('user')->where('id', $this->user_info['id'])->where('much_id', $data['much_id'])->find();
        //帖子详情
        $util = new Util();
        $fa_info = $util->get_page_user($data['id']);

        $page_title = $fa_info['study_title'] == '' ? subtext($fa_info['study_content'], 10) : subtext($fa_info['study_title'], 10);
        $page_title = strip_tags($page_title);
        if (empty($page_title)) {
            if ($fa_info['study_type'] == 0) {
                $page_title = '[图片帖子]';
            }
            if ($fa_info['study_type'] == 1) {
                $page_title = '[语音帖子]';
            }
            if ($fa_info['study_type'] == 2) {
                $page_title = '[视频帖子]';
            }
        }
        //发送模版
        $util->add_template(['much_id' => $data['much_id'],
            'at_id' => 'YL0002',
            'user_id' => $fa_info['user_id'],
            'page' => 'yl_welore/pages/packageA/article/index?id=' . $fa_info['id'] . '&type=' . $fa_info['study_type'],
            'keyword1' => emoji_decode($page_title),
            'keyword2' => emoji_decode($user_info['user_nick_name']),
            'keyword3' => date('Y年m月d日 H:i:s', time()),
        ]);
        Cache::clear("globalIndexCache_".$data['much_id']);
        return $this->json_rewrite($rs);
    }

    /**
     * 帖子置顶
     */
    public function placement()
    {
        $data = input('param.');
        $check = Db::name('paper')->where('id', $data['id'])->where('much_id', $data['much_id'])->find();
        if ($check['whether_type'] != 0 || $check['whether_delete'] == 1) {
            $rs = ['status' => 'error', 'msg' => '帖子已被删除！'];
            return $this->json_rewrite($rs);
        }
        if ($check['topping_time'] == 0) {
            $up = Db::name('paper')->where('id', $data['id'])->where('much_id', $data['much_id'])->update(['topping_time' => time()]);
            if ($up) {
                $rs = ['status' => 'success', 'msg' => '置顶成功！'];
            } else {
                $rs = ['status' => 'error', 'msg' => '置顶失败！'];
            }
        } else {
            $up = Db::name('paper')->where('id', $data['id'])->where('much_id', $data['much_id'])->update(['topping_time' => 0]);
            if ($up) {
                $rs = ['status' => 'success', 'msg' => '取消置顶成功！'];
            } else {
                $rs = ['status' => 'error', 'msg' => '取消置顶失败！'];
            }
        }

        return $this->json_rewrite($rs);
    }

    /**
     * 获取评论详情
     */
    public function get_paper_reply_info()
    {
        $rs = ['status' => 'success', 'msg' => '获取成功'];
        $data = input('param.');
        $util = new Util();
        //评论详情
        $info = Db::name('paper_reply')->alias('r')
            ->join('user u', 'u.id=r.user_id')
            ->where('r.id', $data['id'])
            ->where('r.much_id', $data['much_id'])
            ->field('r.*,u.user_nick_name,u.user_head_sculpture,u.gender')
            ->find();
        if (!empty($info['image_part'])) {
            $info['image_part'] = json_decode($info['image_part'], true);
        }
        $info['reply_content'] = Alternative::ExpressionHtml(emoji_decode($info['reply_content']));

        $info['user_nick_name'] = emoji_decode($info['user_nick_name']);
        $info['apter_time'] = formatTime($info['apter_time']);
        $info['hui_count'] = Db::name('paper_reply_duplex')->where('duplex_status', 1)->where('reply_id', $data['id'])->count();
        $info['avatar_frame'] = $util->get_user_avatar_frame($info['user_id'], $data['much_id']);
        //判断是否有身份铭牌
        if (intval($info['uccid']) != 0) {
            //查询佩戴的身份
            $card_info = Db::name('camouflage_card')->where('id', $info['uccid'])->where('much_id', $data['much_id'])->find();
            $user_card = Db::name('user_camouflage_card')->where('ccid', $info['uccid'])->where('user_id', $info['user_id'])->order('expired_time desc')->where('much_id', $data['much_id'])->find();
            $info['user_head_sculpture'] = $card_info['forgery_head'];
            $info['user_id'] = 0;
            //$info['user_nick_name'] = $card_info['forgery_name'] . '-' . $user_card['id'];
            $info['user_nick_name'] = $card_info['forgery_name'];
        }

        //评论回复
        $info_list = Db::name('paper_reply_duplex')->alias('r')
            ->join('user u', 'u.id=r.user_id')
            ->where('r.reply_id', $data['id'])
            ->where('r.duplex_status', 1)
            ->where('r.whether_delete', 0)
            ->page($data['page'], '10')
            ->field('u.user_head_sculpture,u.gender,u.user_nick_name,r.re_uccid,r.id,r.uccid,r.user_id,r.reply_id,r.reply_user_id,r.duplex_content,r.duplex_time,r.much_id')
            ->select();
        if (!empty($info_list)) {
            foreach ($info_list as $k => $v) {
                $info_list[$k]['duplex_content'] = Alternative::ExpressionHtml(emoji_decode($v['duplex_content']));
                $info_list[$k]['duplex_time'] = formatTime($v['duplex_time']);
                $info_list[$k]['user_nick_name'] = emoji_decode($v['user_nick_name']);

                if ($info_list[$k]['reply_user_id'] != 0) {
                    $user = Db::name('user')->where('id', $v['reply_user_id'])->where('much_id', $data['much_id'])->find();
                    $info_list[$k]['hui_nick_name'] = emoji_decode($user['user_nick_name']);

                    if (intval($v['re_uccid']) != 0) {
                        //查询佩戴的身份
                        $card_info_a = Db::name('camouflage_card')->where('id', $v['re_uccid'])->where('much_id', $data['much_id'])->find();
                        $user_card_b = Db::name('user_camouflage_card')->where('ccid', $v['re_uccid'])->where('user_id', $v['reply_user_id'])->order('expired_time desc')->where('much_id', $data['much_id'])->find();
                        //$info_list[$k]['hui_nick_name'] = $card_info_a['forgery_name'] . '-' . $user_card_b['id'];
                        $info_list[$k]['hui_nick_name'] = $card_info_a['forgery_name'];
                    }
                }
                $info_list[$k]['avatar_frame'] = $util->get_user_avatar_frame($v['user_id'], $data['much_id']);

                if (intval($v['uccid']) != 0) {
                    //查询佩戴的身份
                    $card_info_a = Db::name('camouflage_card')->where('id', $v['uccid'])->where('much_id', $data['much_id'])->find();
                    $user_card_b = Db::name('user_camouflage_card')->where('ccid', $v['uccid'])->where('user_id', $v['user_id'])->order('expired_time desc')->where('much_id', $data['much_id'])->find();
                    $info_list[$k]['user_head_sculpture'] = $card_info_a['forgery_head'];
                    $info_list[$k]['user_id'] = 0;
                    //$info_list[$k]['user_nick_name'] = $card_info_a['forgery_name'] . '-' . $user_card_b['id'];
                    $info_list[$k]['user_nick_name'] = $card_info_a['forgery_name'];
                }


            }
        }

        $rs['info'] = $info;
        $rs['list'] = $info_list;
        return $this->json_rewrite($rs);
    }

    /**
     * 发布评论回复
     */
    public function add_paper_reply_duplex()
    {
        $rs = ['status' => 'success', 'msg' => '回复成功'];
        $data = input('param.');
        if ($this->user_info['tourist'] == 1) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '请登陆后回复！']);
        }
        $util = new Util();
        //获取是否打开强制手机号
        $authority = Db::name('authority')->where('much_id', $data['much_id'])->find();
        if ($this->version == 0) {
            if ($authority['re_force_phone_arbor'] == 1 && empty($this->user_info['user_phone'])) {
                return $this->json_rewrite(['status' => 'error', 'id' => $this->version, 'msg' => '请绑定手机号！']);
            }
        }
        if ($this->paper_smingle['discuss_number_limit'] != 0) {
            $dux_paper = $util->get_dupx_paper($data['id'])['paper_id'];
            //查询当前回复总数
            $dux = Db::name('paper_reply_duplex')->alias('p')
                ->join('paper_reply c', 'c.id=p.reply_id', 'LEFT')
                ->where('p.user_id', $this->user_info['id'])
                ->where('c.paper_id', $dux_paper)
                ->whereTime('duplex_time', 'd')
                ->count();
            if ($dux >= $this->paper_smingle['discuss_number_limit']) {
                return $this->json_rewrite(['status' => 'error', 'msg' => '当前内容回复次数今日已达上限']);
            }
        }
        if (empty($data['duplex_content'])) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '回复内容不能为空！']);
        }
        $check_title = $util->get_check_msg($data['duplex_content'], $data['much_id'], $data['openid']);
        if ($check_title['status'] == 'error') {
            return $this->json_rewrite($check_title);
        }
        //判断身份铭牌
        if (isset($data['name_card']) && intval($data['name_card']) != 0) {
            //判断当前是否是否过期
            $check_name_card = Db::name('user_camouflage_card')
                ->where('ccid', $data['name_card'])
                ->where('user_id', $this->user_info['id'])
                ->where('much_id', $data['much_id'])
                ->where('expired_time', '> time', date('Y-m-d H:i:s', time()))
                ->find();
            if (empty($check_name_card)) {
                return $this->json_rewrite(['status' => 'name_error', 'id' => 0, 'msg' => '身份铭牌已过期！']);
            } else {
                $dd['uccid'] = $data['name_card'];
            }
        } else {
            $dd['uccid'] = 0;
        }

        if ($authority['engrave_arbor'] == 0) {
            $dd['uccid'] = 0;
        }

        //评论详情
        $info = Db::name('paper_reply')->alias('r')
            ->join('user u', 'u.id=r.user_id')
            ->where('r.id', $data['id'])
            ->where('r.much_id', $data['much_id'])
            ->field('r.*,u.user_nick_name,u.user_head_sculpture')
            ->find();

        $q = $util->get_user_applaud($info['id']);

        $check_banned = Db::name('user_banned')->where('tory_id', $q['tory_id'])->where('user_id', $this->user_info['id'])->where('much_id', $data['much_id'])->find();
        if ($check_banned['refer_time'] > time()) {
            return $this->json_rewrite(['status' => 'error', 'id' => 0, 'msg' => '您已被禁言，解除时间:' . date('Y-m-d H:i:s', $check_banned['refer_time'])]);
        }


        $dd['user_id'] = $this->user_info['id'];
        $dd['reply_id'] = $data['id'];
        $dd['duplex_content'] = $this->safe_html(emoji_encode($data['duplex_content']));
        $dd['duplex_time'] = time();
        $dd['much_id'] = $data['much_id'];
        $dd['reply_user_id'] = $data['user_id'];
        //获取那条回复的回复
        if (isset($data['and_id']) && $data['and_id'] != 0) {
            $res = Db::name('paper_reply_duplex')->where('much_id', $data['much_id'])->where('id', $data['and_id'])->find();
            $dd['reply_user_id'] = $res['user_id'];
            $dd['re_uccid'] = $res['uccid'];
        }
        //是否开启自动审核//1开启0关闭
        if ($this->paper_smingle['discuss_auto_review'] == 0) {
            $dd['duplex_status'] = 0;
        } else {
            $dd['duplex_status'] = 1;
        }

        $ins = Db::name('paper_reply_duplex')->insert($dd);
        if (!$ins) {
            $rs = ['status' => 'error', 'msg' => '回复失败'];
        }
        //查询是否是自己回复自己
        //if ($info['user_id'] != $this->user_info['id']) {
        if ($info['reply_type'] == 1) {
            $keyword1 = '[语音]';
        }
        if ($info['reply_type'] == 0) {
            if (empty($info['reply_content'])) {
                $keyword1 = '[图片]';
            } else if (empty($info['image_part'])) {
                $keyword1 = emoji_decode($info['reply_content']);
            } else {
                $keyword1 = emoji_decode($info['reply_content']);
            }

        }
        if ($dd['uccid'] != 0) {
            $card_info_a = Db::name('camouflage_card')->where('id', $dd['uccid'])->where('much_id', $data['much_id'])->find();
            //$user_card_b = Db::name('user_camouflage_card')->where('ccid', $dd['uccid'])->where('user_id', $this->user_info['id'])->order('expired_time desc')->where('much_id', $data['much_id'])->find();
            //$this->user_info['user_nick_name'] = $card_info_a['forgery_name'] . '-' . $user_card_b['id'];
            $this->user_info['user_nick_name'] = $card_info_a['forgery_name'];
        }
        //是否开启自动审核//1开启0关闭
        if ($this->paper_smingle['discuss_auto_review'] == 0) {
            Db::name('prompt_msg')->insert(['capriole' => 5, 'tyid' => 0, 'msg_time' => time(), 'type' => 0, 'retter' => '用户：' . $this->user_info['user_nick_name'] . '发布了一条评论回复待审核！', 'status' => 0, 'much_id' => $data['much_id']]);
            $notices = Db::name('prompt_msg')
                ->where('status', 0)
                ->where('type', 0)
                ->where('much_id', $data['much_id'])
                ->count('*');
            cache('notices_' . $data['much_id'], $notices);
            return $this->json_rewrite(['status' => 'error', 'msg' => '回复成功,请等待审核！']);
        }
        $util->add_template([
            'much_id' => $data['much_id'],
            'at_id' => 'YL0004',
            'user_id' => $info['user_id'],
            'page' => 'yl_welore/pages/packageA/article/index?id=' . $info['paper_id'],
            'keyword1' => $keyword1,
            'keyword2' => emoji_decode($this->user_info['user_nick_name']),
            'keyword3' => $data['duplex_content'],
            'keyword4' => date('Y年m月d日 H:i:s', time())
        ]);
        return $this->json_rewrite($rs);
    }

    /**
     * 推荐
     */
    public function set_essence()
    {
        $data = input('param.');
        $Util = new Util();
        //帖子详情
        $paper = Db::name('paper')->where('id', $data['id'])->where('much_id', $data['much_id'])->find();
        //是否是圈主
        $user = $Util->check_qq($data['openid'], $paper['tory_id']);

        $cg = Db::name('user_maker')->where('much_id', $data['much_id'])->where('user_open_id', $data['openid'])->where('status', '1')->count();

        $check = 0;
        if ($user == 'no' || $user == 'xiao') {
            $check = 0;
        } else {
            $check = 1;
        }
        if ($cg == 1) {
            $check = 1;
        }
        if ($check == 1) {
            $rs = Db::name('paper')->where('id', $data['id'])->where('much_id', $data['much_id'])->update(['essence_time' => time()]);
            if ($rs) {
                return $this->json_rewrite(['status' => 'success', 'msg' => '推荐成功！']);
            } else {
                return $this->json_rewrite(['status' => 'error', 'msg' => '推荐失败！']);
            }
        } else {
            return $this->json_rewrite(['status' => 'error', 'msg' => '您没有权限！']);
        }
    }

    /**
     * 删除推荐
     */
    public function del_essence()
    {
        $data = input('param.');
        $Util = new Util();
        //帖子详情
        $paper = Db::name('paper')->where('id', $data['id'])->where('much_id', $data['much_id'])->find();
        //是否是圈主
        $user = $Util->check_qq($data['openid'], $paper['tory_id']);

        $cg = Db::name('user_maker')->where('much_id', $data['much_id'])->where('user_open_id', $data['openid'])->where('status', '1')->count();

        $check = 0;
        if ($user == 'no' || $user == 'xiao') {
            $check = 0;
        } else {
            $check = 1;
        }
        if ($cg == 1) {
            $check = 1;
        }
        if ($check == 1) {
            $rs = Db::name('paper')->where('id', $data['id'])->where('much_id', $data['much_id'])->update(['essence_time' => 0]);
            if ($rs) {
                return $this->json_rewrite(['status' => 'success', 'msg' => '取消推荐成功！']);
            } else {
                return $this->json_rewrite(['status' => 'error', 'msg' => '取消推荐失败！']);
            }
        } else {
            return $this->json_rewrite(['status' => 'error', 'msg' => '您没有权限！']);
        }
    }

    public function set_brisk_team()
    {
        $data = input('param.');

        $cg = Db::name('user_maker')->where('much_id', $data['much_id'])->where('user_open_id', $data['openid'])->where('status', '1')->count();
        $check = 0;
        if ($cg == 1) {
            $check = 1;
        }
        if ($check == 1) {
            if ($data['is_approve'] == 0) {
                $is_approve = 1;
            } else {
                $is_approve = 0;
            }
            $rs = Db::name('brisk_team')->where('paper_id', $data['paper_id'])->where('much_id', $data['much_id'])->update(['is_approve' => $is_approve]);
            if ($rs) {
                return $this->json_rewrite(['status' => 'success', 'msg' => '成功！']);
            } else {
                return $this->json_rewrite(['status' => 'error', 'msg' => '失败！']);
            }
        } else {
            return $this->json_rewrite(['status' => 'error', 'msg' => '您没有权限！']);
        }
    }

    /**
     * 修改帖子样式
     */
    public function set_img_show_type()
    {
        $data = input('param.');
        $info = Db::name('paper')->where('id', $data['id'])->where('much_id', $data['much_id'])->find();
        if ($info['user_id'] != $this->user_info['id']) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '您没有权限！']);
        }
        $up['img_show_type'] = $info['img_show_type'] == 0 ? 1 : 0;
        $ins = Db::name('paper')->where('id', $data['id'])->update($up);
        if ($ins !== false) {
            return $this->json_rewrite(['status' => 'success', 'msg' => '修改成功！']);
        }
        return $this->json_rewrite(['status' => 'error', 'msg' => '修改失败！']);
    }

    /**
     * 删除评论回复
     */
    public function get_reply_user_del()
    {
        $data = input('param.');
        $info = Db::name('paper_reply_duplex')->where('id', $data['id'])->where('much_id', $data['much_id'])->find();
        $Util = new Util();
        //帖子详情
        $paper_reply = Db::name('paper_reply')->where('id', $info['reply_id'])->where('much_id', $data['much_id'])->find();
        //回复详情
        $paper = Db::name('paper')->where('id', $paper_reply['paper_id'])->where('much_id', $data['much_id'])->find();
        //是否是圈主
        $user = $Util->check_qq($data['openid'], $paper['tory_id']);

        $cg = Db::name('user_maker')->where('much_id', $data['much_id'])->where('user_open_id', $data['openid'])->where('status', '1')->count();
        $key = 0;//
        if ($info['user_id'] == $this->user_info['id']) {
            $key = 1;
        }
        if ($user == 'da' || $user == 'xiao') {
            $key = 1;
        }
        if ($cg == 1) {
            $key = 1;
        }
        if ($key == 0) {
            return $this->json_rewrite(['status' => 'error', 'msg' => '您没有权限！']);
        }
        $ins = Db::name('paper_reply_duplex')->where('id', $data['id'])->update(['whether_delete' => 3, 'whetd_time' => time(), 'whether_reason' => '自己删除！']);
        if ($ins !== false) {
            return $this->json_rewrite(['status' => 'success', 'msg' => '删除成功！']);
        }
        return $this->json_rewrite(['status' => 'error', 'msg' => '删除失败！']);
    }
}