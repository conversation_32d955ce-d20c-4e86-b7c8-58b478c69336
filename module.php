<?php


defined('IN_IA') or exit('Access Denied');

/**
 * 模块入口文件 (初始化)
 */
class Yl_weloreModule extends WeModule
{
    private $make_variable;

    /**
     * 构造方法
     */
    public function __construct()
    {
        global $_W;
        $this->make_variable = $_W;
    }

    /**
     * 生成一个密码学安全的随机令牌字符串
     * @param int $length
     * @return string
     */
    private function generateSecureToken($length = 64)
    {
        $byteLength = (int)ceil($length / 2);
        // 在PHP 5.6中，openssl_random_pseudo_bytes 是生成安全随机数的最佳选择
        $randomBytes = openssl_random_pseudo_bytes($byteLength, $isStrong);
        if ($isStrong === false) {
            // 如果OpenSSL无法生成强随机数，使用降级方案
            // 基于时间戳、进程ID和多个随机源生成相对安全的随机数
            $fallbackSources = array(
                microtime(true),
                getmypid(),
                mt_rand(),
                uniqid('', true),
                memory_get_usage(),
                isset($_SERVER['REQUEST_TIME_FLOAT']) ? $_SERVER['REQUEST_TIME_FLOAT'] : time()
            );
            $fallbackString = implode('|', $fallbackSources);
            // 使用多次哈希增强随机性
            $hash1 = hash('sha256', $fallbackString);
            $hash2 = hash('sha256', $hash1 . mt_rand());
            return substr($hash2, 0, $length);
        }
        return bin2hex($randomBytes);
    }

    /**
     * 创建增强会话指纹 - 稳定性优化，与LoginCheck.php算法完全一致
     * @param string|null $sessionSalt 会话盐值
     * @return string SHA256哈希指纹
     */
    private function createSessionFingerprint($sessionSalt = null)
    {
        // 收集稳定的特征信息（移除易变的网络层信息，与LoginCheck.php完全一致）
        $fingerprintComponents = array(
            // HTTP头部信息（相对稳定）
            'user_agent' => isset($_SERVER['HTTP_USER_AGENT']) ? $_SERVER['HTTP_USER_AGENT'] : '',
            'accept_language' => isset($_SERVER['HTTP_ACCEPT_LANGUAGE']) ? $_SERVER['HTTP_ACCEPT_LANGUAGE'] : '',
            'accept_encoding' => isset($_SERVER['HTTP_ACCEPT_ENCODING']) ? $_SERVER['HTTP_ACCEPT_ENCODING'] : '',
            // 会话盐值（确保唯一性）
            'session_salt' => $sessionSalt
        );

        // 过滤空值并拼接
        $filteredComponents = array_filter($fingerprintComponents, function ($value) {
            return !empty($value);
        });

        $signatureString = implode('|', $filteredComponents);

        // 生成SHA256指纹
        return hash('sha256', $signatureString);
    }

    /**
     * 生成会话盐值 - 与LoginCheck.php算法一致
     * @return string 随机盐值
     */
    private function generateSessionSalt()
    {
        // 生成基于时间戳和随机数的盐值
        $timestamp = microtime(true);
        $randomBytes = $this->generateSecureToken(32);
        $saltComponents = array(
            $timestamp,
            $randomBytes,
            mt_rand(100000, 999999)
        );

        return hash('sha256', implode('|', $saltComponents));
    }

    /**
     * 执行模块初始化操作
     */
    public function execute()
    {
        // 验证模块核心文件
        $this->checkModuleFile();

        try {
            // 为当前会话生成安全凭证
            $sessionToken = $this->generateSecureToken();
            $sessionSalt = $this->generateSessionSalt();
            $sessionFingerprint = $this->createSessionFingerprint($sessionSalt);

            // 验证生成的凭证有效性
            if (empty($sessionToken) || empty($sessionSalt) || empty($sessionFingerprint)) {
                throw new Exception('会话凭证生成失败，无法继续登录流程。');
            }

            // 定义公共变量并设置安全Cookie（包含token和salt）
            $this->causeFickle($sessionToken, $sessionSalt);

            // 将安全凭证存入数据库
            $this->loginChecking($this->make_variable, $sessionToken, $sessionFingerprint);

        } catch (Exception $e) {
            // 记录错误信息并向用户显示友好的错误消息
            error_log('yl_welore模块登录凭证生成失败: ' . $e->getMessage());
            itoast('系统初始化失败，请稍后重试或联系管理员。', referer(), 'error');
            return;
        }

        // 跳转到独立后台
        $this->module();
    }

    /**
     * 验证模块核心文件
     */
    private function checkModuleFile()
    {
        $module_file = __DIR__ . DIRECTORY_SEPARATOR . 'web' . DIRECTORY_SEPARATOR . 'index.php';
        !file_exists($module_file) && itoast('模块文件不存在', referer(), 'error');
    }

    /**
     * 定义公共变量并设置安全Cookie - 包含token和salt
     * @param string $sessionToken
     * @param string $sessionSalt
     */
    private function causeFickle($sessionToken, $sessionSalt)
    {
        $this->make_variable['nowTime'] = time();

        // 获取动态插件名称
        $pluginName = $this->getPluginName();
        $tokenCookieName = $pluginName . '_session_token';
        $saltCookieName = $pluginName . '_session_salt';

        // 设置token cookie
        $tokenCookieHeader = sprintf(
            '%s=%s; Path=/; SameSite=Lax; Secure; HttpOnly',
            $tokenCookieName,
            $sessionToken
        );
        header("Set-Cookie: {$tokenCookieHeader}", false);

        // 设置salt cookie
        $saltCookieHeader = sprintf(
            '%s=%s; Path=/; SameSite=Lax; Secure; HttpOnly',
            $saltCookieName,
            $sessionSalt
        );
        header("Set-Cookie: {$saltCookieHeader}", false);
    }

    /**
     * 获取当前插件目录名称 - 与LoginCheck.php保持一致
     * @return string 插件名称
     */
    private function getPluginName()
    {
        // 使用__DIR__魔术常量获取当前文件路径
        $currentPath = __DIR__;

        // 解析路径：从 /path/to/addons/plugin_name/
        // 提取出 plugin_name
        $pathSegments = explode(DIRECTORY_SEPARATOR, $currentPath);

        // 查找addons目录的位置
        $addonsIndex = array_search('addons', $pathSegments);

        // 返回插件名称
        return $pathSegments[$addonsIndex + 1];
    }


    /**
     * 设置登录检查 - Salt存储在Cookie中
     * @param array $make
     * @param string $sessionToken
     * @param string $sessionFingerprint
     */
    private function loginChecking($make, $sessionToken, $sessionFingerprint)
    {
        $nowLoginIp = $this->getRealIp();
        $this->make_variable['nowTime'] = time();

        $loginTime = time();
        $insertData = array(
            ':user_name' => $make['user']['username'],
            ':role' => $make['role'],
            ':uniacid' => $make['uniacid'],
            ':access_type' => 1,
            ':login_ip' => $nowLoginIp,
            ':login_time' => $loginTime,
            ':session_token' => $sessionToken,
            ':session_fingerprint' => $sessionFingerprint,
            ':token_last_refreshed_at' => $loginTime
        );

        $loginCheckingSQL = "INSERT INTO `yl_welore_login_checking` (`user_name`, `role`, `uniacid`, `access_type`, `login_ip`, `login_time`, `session_token`, `session_fingerprint`, `token_last_refreshed_at`) VALUES (:user_name, :role, :uniacid, :access_type, :login_ip, :login_time, :session_token, :session_fingerprint, :token_last_refreshed_at);";

        pdo_query($loginCheckingSQL, $insertData);
    }

    /*
     * 获取用户IP地址
     */
    private function getRealIp()
    {
        $ip = false;
        if (!empty($_SERVER["HTTP_CLIENT_IP"])) {
            $ip = $_SERVER["HTTP_CLIENT_IP"];
        }
        if (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
            $ips = explode(",", $_SERVER['HTTP_X_FORWARDED_FOR']);
            foreach ($ips as $subIp) {
                $subIp = trim($subIp);
                if (!preg_match("/^(10\.|172\.(1[6-9]|2[0-9]|3[0-1])\.|192\.168\.)/i", $subIp)) {
                    $ip = $subIp;
                    return $ip;
                }
            }
        }
        $remoteAddr = isset($_SERVER['REMOTE_ADDR']) ? $_SERVER['REMOTE_ADDR'] : '0.0.0.0';
        return ($ip ?: $remoteAddr);
    }

    /**
     * 跳转到模块后台
     */
    private function module()
    {
        global $_W;
        $homePage = 'index.php';
        $url = "{$_W['siteroot']}addons/{$_W['current_module']['name']}/web/{$homePage}";
        header('Location:' . $url);
        exit;
    }

    /**
     * 执行模块初始化
     */
    public function welcomeDisplay()
    {
        $this->execute();
    }
}
